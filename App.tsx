
/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import { NewAppScreen } from '@react-native/new-app-screen';
import { StatusBar, StyleSheet, useColorScheme, View, Text, Button, Alert, SafeAreaView } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import CustomView from './demo/HomeBottomItem';
import DeviceList from './demo/DeviceList';
import MyAccount from './demo/MyAccount';
import React, { useState } from 'react';

function App() {  

  // 使用 selectedTab 状态来跟踪当前选中的标签
  const [selectedTab, setSelectedTab] = useState('home'); // 默认选中 home 标签
  const isDarkMode = useColorScheme() === 'dark';
  // 处理标签点击事件的函数
  const handleTabPress = (tabName: string) => {
    setSelectedTab(tabName);
  };

  // 根据选中的tab渲染不同的内容
  const renderContent = () => {

    switch (selectedTab) {
      case 'home':
        return <DeviceList />;
      case 'account':
        return <MyAccount />;
      default:
        return <DeviceList />;
    }

  };

  return (
    <SafeAreaView style={styles.container}>
      
      <StatusBar barStyle="dark-content"></StatusBar>

      <View style={styles.contentContainer}>
        {renderContent()}
      </View>
  
      <View style={styles.bottomViewItem}>
        <CustomView
          text='Home'
          normalUrl={require('./assets/images/home_normal.png')}
          clickedUrl={require('./assets/images/home_clicked.png')}
          status={selectedTab === 'home'}
          onPress={() => handleTabPress('home')}
        />

        <CustomView
          text='Account'
          normalUrl={require('./assets/images/icon_account_normal.png')}
          clickedUrl={require('./assets/images/icon_account_clicked.png')}
          status={selectedTab === 'account'}
          onPress={() => handleTabPress('account')}
        />
      </View>
    </SafeAreaView>
  );
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  contentContainer: {
    flex: 1,
    paddingTop: StatusBar.currentHeight || 0, // 设置顶部内边距为状态栏高度
  },

  textStyleNormal: {
    fontSize: 16,
    color: 'black',
    textAlign: 'center', // 设置文字水平居中
    margin: 10,
    flex: 1
  },
  textStyleClicked: {
    fontSize: 16,
    color: 'green',
    textAlign: 'center', // 设置文字水平居中
    margin: 10
  },
  bottomView: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'gray',
    flexDirection: 'row'
  },
  bottomViewItem: {
    flex: 1,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'white',
    flexDirection: 'row', // 水平排列
    justifyContent: 'space-between', // 两端对齐
  },

});

export default App;
