package com.chervon.utils;

import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import java.util.HashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 视频封面提取工具类
 */
public class VideoThumbnailUtils {

    private static final ExecutorService executorService = Executors.newCachedThreadPool();
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * 同步获取视频封面
     *
     * @param videoUrl 视频地址
     * @return 视频封面图片，失败返回null
     */
    public static Bitmap getVideoThumbnail(String videoUrl) {
        if (TextUtils.isEmpty(videoUrl)) {
            return null;
        }

        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            if (videoUrl.startsWith("http") || videoUrl.startsWith("https")) {
                // 在线视频
                retriever.setDataSource(videoUrl, new HashMap<>());
            } else {
                // 本地视频
                retriever.setDataSource(videoUrl);
            }
            // 获取视频的第一帧作为封面
            return retriever.getFrameAtTime(0);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 异步获取视频封面
     *
     * @param videoUrl 视频地址
     * @param callback 回调接口
     */
    public static void getVideoThumbnailAsync(String videoUrl, ThumbnailCallback callback) {
        if (callback == null) {
            return;
        }

        if (TextUtils.isEmpty(videoUrl)) {
            mainHandler.post(() -> callback.onFailure(new IllegalArgumentException("Video url cannot be empty")));
            return;
        }

        executorService.execute(() -> {
            try {
                Bitmap thumbnail = getVideoThumbnail(videoUrl);
                mainHandler.post(() -> {
                    if (thumbnail != null) {
                        callback.onSuccess(thumbnail);
                    } else {
                        callback.onFailure(new Exception("Failed to extract thumbnail"));
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onFailure(e));
            }
        });
    }

    /**
     * 获取指定时间点的视频帧
     *
     * @param videoUrl 视频地址
     * @param timeUs 时间点(微秒)
     * @return 指定时间点的视频帧，失败返回null
     */
    public static Bitmap getVideoFrameAtTime(String videoUrl, long timeUs) {
        if (TextUtils.isEmpty(videoUrl)) {
            return null;
        }

        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            if (videoUrl.startsWith("http") || videoUrl.startsWith("https")) {
                retriever.setDataSource(videoUrl, new HashMap<>());
            } else {
                retriever.setDataSource(videoUrl);
            }
            return retriever.getFrameAtTime(timeUs);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 封面获取回调接口
     */
    public interface ThumbnailCallback {
        void onSuccess(Bitmap thumbnail);
        void onFailure(Exception e);
    }
}