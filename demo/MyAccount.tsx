import React, { Component } from 'react';
import {StyleSheet, Text, View } from 'react-native';
import { Image ,ImageBackground} from 'react-native';

class MyAccount extends Component {
  render() {
    return (
      <View style={styles.container}>
        <ImageBackground source={require('../assets/images/usercenter_bg.png')} style={styles.imageTop}></ImageBackground>
        <Text style={styles.title}>My Account</Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  imageTop:{
    height:331/2,
    
  }
});

export default MyAccount;
