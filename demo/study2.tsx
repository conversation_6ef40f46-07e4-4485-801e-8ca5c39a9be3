import React, { useState } from "react";        
import { StatusBar, StyleSheet, View ,Text,SafeAreaView} from 'react-native'; 


function App() {
  return (

  <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <View style={styles.contentContainer}>
        <Text>this is second page</Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    paddingTop: StatusBar.currentHeight || 0, 
  },
});

export default App;
    