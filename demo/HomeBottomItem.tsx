import React from 'react';
import { View, Image, TouchableOpacity, StyleSheet, Text } from 'react-native';

interface CustomViewProps {
  normalUrl: number;
  clickedUrl: number;
  status: boolean;
  text: string;
  onPress?: () => void; // 添加 onPress 回调属性
}

class CustomView extends React.Component<CustomViewProps> {
  render() {
    const { normalUrl, clickedUrl, status, text, onPress } = this.props;

    return (
      <TouchableOpacity onPress={onPress} style={styles.touchable}>
        <View style={styles.container}>
          <Image
            source={status ? clickedUrl : normalUrl}
            style={styles.image}
          />
          <Text style={status ? styles.textStyleClicked : styles.textStyleNormal}>
            {text}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  touchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    justifyContent: 'center', // 垂直居中
    alignItems: 'center',     // 水平居中
  },
  image: {
    width: 26,
    height: 26,
    margin: 5,
    justifyContent: 'center', // 垂直居中
    alignItems: 'center',     // 水平居中
  },
  textStyleNormal: {
    fontSize: 11,
    color: 'black',
    textAlign: 'center', // 设置文字水平居中
    marginTop: 5,
    justifyContent: 'center', // 垂直居中
    alignItems: 'center',     // 水平居中
  },
  textStyleClicked: {
    fontSize: 11,
    color: 'green',
    textAlign: 'center', // 设置文字水平居中
    marginTop: 5,
    justifyContent: 'center', // 垂直居中
    alignItems: 'center',     // 水平居中
  }
});

export default CustomView;
